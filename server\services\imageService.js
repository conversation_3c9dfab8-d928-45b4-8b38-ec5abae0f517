import sharp from 'sharp'

export const processImage = async (buffer, options = {}) => {
  try {
    const {
      width = 512,
      height = 512,
      quality = 90,
      format = 'png'
    } = options

    console.log('🖼️ Processing image with options:', { width, height, quality, format })

    // Get image metadata
    const metadata = await sharp(buffer).metadata()
    console.log('📊 Original image metadata:', {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      size: `${(buffer.length / 1024).toFixed(2)} KB`
    })

    // Process the image
    let processedImage = sharp(buffer)

    // Resize if needed
    if (width || height) {
      processedImage = processedImage.resize(width, height, {
        fit: 'cover',
        position: 'center'
      })
    }

    // Convert format and optimize
    switch (format.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        processedImage = processedImage.jpeg({ quality })
        break
      case 'webp':
        processedImage = processedImage.webp({ quality })
        break
      case 'png':
      default:
        processedImage = processedImage.png({ 
          quality,
          compressionLevel: 9,
          adaptiveFiltering: true
        })
        break
    }

    const result = await processedImage.toBuffer()
    
    console.log('✅ Image processed successfully:', {
      originalSize: `${(buffer.length / 1024).toFixed(2)} KB`,
      processedSize: `${(result.length / 1024).toFixed(2)} KB`,
      compression: `${(((buffer.length - result.length) / buffer.length) * 100).toFixed(1)}%`
    })

    return result

  } catch (error) {
    console.error('❌ Image processing failed:', error)
    throw new Error(`Image processing failed: ${error.message}`)
  }
}

export const validateImage = (buffer) => {
  try {
    // Check if buffer is valid
    if (!buffer || buffer.length === 0) {
      throw new Error('Empty image buffer')
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024
    if (buffer.length > maxSize) {
      throw new Error(`Image too large. Maximum size is ${maxSize / 1024 / 1024}MB`)
    }

    // Basic image format validation
    const imageSignatures = {
      png: [0x89, 0x50, 0x4E, 0x47],
      jpeg: [0xFF, 0xD8, 0xFF],
      gif: [0x47, 0x49, 0x46],
      webp: [0x52, 0x49, 0x46, 0x46]
    }

    let detectedFormat = null
    for (const [format, signature] of Object.entries(imageSignatures)) {
      if (signature.every((byte, index) => buffer[index] === byte)) {
        detectedFormat = format
        break
      }
    }

    if (!detectedFormat) {
      throw new Error('Unsupported image format. Please use PNG, JPEG, GIF, or WebP')
    }

    return {
      valid: true,
      format: detectedFormat,
      size: buffer.length
    }

  } catch (error) {
    return {
      valid: false,
      error: error.message
    }
  }
}

export const generateThumbnail = async (buffer, size = 128) => {
  try {
    console.log(`🖼️ Generating ${size}x${size} thumbnail`)

    const thumbnail = await sharp(buffer)
      .resize(size, size, {
        fit: 'cover',
        position: 'center'
      })
      .png({ quality: 80 })
      .toBuffer()

    console.log('✅ Thumbnail generated successfully')
    return thumbnail

  } catch (error) {
    console.error('❌ Thumbnail generation failed:', error)
    throw new Error(`Thumbnail generation failed: ${error.message}`)
  }
}
