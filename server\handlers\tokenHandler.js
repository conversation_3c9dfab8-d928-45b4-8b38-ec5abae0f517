import { createToken } from '../services/tokenService.js'
import { uploadToIPFS, uploadMetadataToIPFS } from '../services/ipfsService.js'
import { processImage } from '../services/imageService.js'

export const createTokenHandler = async (req, res) => {
  try {
    console.log('🚀 Token creation request received')
    
    // Extract form data
    const {
      name,
      symbol,
      description,
      totalSupply,
      decimals,
      revokeAuthorities,
      walletAddress
    } = req.body

    // Validate required fields
    if (!name || !symbol || !description || !totalSupply || !walletAddress) {
      return res.status(400).json({
        error: 'Missing required fields: name, symbol, description, totalSupply, walletAddress'
      })
    }

    console.log('📝 Token details:', {
      name,
      symbol,
      description: description.substring(0, 50) + '...',
      totalSupply,
      decimals,
      revokeAuthorities,
      walletAddress: walletAddress.substring(0, 8) + '...',
      hasImage: !!req.file
    })

    let imageUri = null

    // Process and upload image if provided
    if (req.file) {
      console.log('🖼️ Processing image...')
      
      try {
        // Process image (resize, optimize)
        const processedImage = await processImage(req.file.buffer, {
          width: 512,
          height: 512,
          quality: 90
        })

        // Upload image to IPFS
        console.log('📤 Uploading image to IPFS...')
        imageUri = await uploadToIPFS(processedImage, `${symbol.toLowerCase()}-logo.png`)
        console.log('✅ Image uploaded:', imageUri)
      } catch (imageError) {
        console.error('❌ Image processing/upload failed:', imageError)
        return res.status(400).json({
          error: 'Failed to process or upload image: ' + imageError.message
        })
      }
    }

    // Create metadata object
    const metadata = {
      name,
      symbol,
      description,
      image: imageUri || '',
      attributes: [],
      properties: {
        files: imageUri ? [{
          uri: imageUri,
          type: 'image/png'
        }] : [],
        category: 'image'
      },
      creators: [{
        address: walletAddress,
        verified: false,
        share: 100
      }]
    }

    // Upload metadata to IPFS
    console.log('📤 Uploading metadata to IPFS...')
    const metadataUri = await uploadMetadataToIPFS(metadata, `${symbol.toLowerCase()}-metadata.json`)
    console.log('✅ Metadata uploaded:', metadataUri)

    // Create token on Solana
    console.log('🪙 Creating token on Solana...')
    const tokenResult = await createToken({
      name,
      symbol,
      uri: metadataUri,
      decimals: parseInt(decimals) || 9,
      totalSupply: totalSupply,
      revokeAuthorities: revokeAuthorities === 'true',
      walletAddress
    })

    console.log('✅ Token created successfully!')
    console.log('📊 Result:', {
      signature: tokenResult.signature,
      tokenAddress: tokenResult.tokenAddress,
      metadataUri
    })

    // Return success response
    res.json({
      success: true,
      signature: tokenResult.signature,
      tokenAddress: tokenResult.tokenAddress,
      metadataUri,
      imageUri,
      message: 'Token created successfully!'
    })

  } catch (error) {
    console.error('❌ Token creation failed:', error)
    
    // Determine error type and status code
    let statusCode = 500
    let errorMessage = error.message || 'Token creation failed'

    if (error.message?.includes('insufficient funds')) {
      statusCode = 400
      errorMessage = 'Insufficient SOL balance for token creation'
    } else if (error.message?.includes('wallet')) {
      statusCode = 400
      errorMessage = 'Invalid wallet address or wallet connection issue'
    } else if (error.message?.includes('network')) {
      statusCode = 503
      errorMessage = 'Solana network connection issue. Please try again.'
    }

    res.status(statusCode).json({
      error: errorMessage,
      ...(process.env.NODE_ENV === 'development' && { 
        details: error.stack,
        originalError: error.message 
      })
    })
  }
}
