import React, { useState } from 'react'
import { useWallet } from '@solana/wallet-adapter-react'
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'
import { AlertCircle, Wallet } from 'lucide-react'

import TokenForm from './TokenForm'
import TransactionStatus from './TransactionStatus'

export interface TokenFormData {
  name: string
  symbol: string
  description: string
  totalSupply: string
  decimals: number
  image: File | null
  revokeAuthorities: boolean
}

export interface TransactionState {
  status: 'idle' | 'uploading' | 'creating' | 'success' | 'error'
  message: string
  txSignature?: string
  tokenAddress?: string
  metadataUri?: string
}

const TokenCreator: React.FC = () => {
  const { connected, publicKey } = useWallet()
  const [transactionState, setTransactionState] = useState<TransactionState>({
    status: 'idle',
    message: ''
  })

  const handleTokenCreation = async (formData: TokenFormData) => {
    if (!connected || !publicKey) {
      setTransactionState({
        status: 'error',
        message: 'Please connect your wallet first'
      })
      return
    }

    try {
      setTransactionState({
        status: 'uploading',
        message: 'Uploading image and metadata to IPFS...'
      })

      // Create FormData for file upload
      const uploadData = new FormData()
      uploadData.append('name', formData.name)
      uploadData.append('symbol', formData.symbol)
      uploadData.append('description', formData.description)
      uploadData.append('totalSupply', formData.totalSupply)
      uploadData.append('decimals', formData.decimals.toString())
      uploadData.append('revokeAuthorities', formData.revokeAuthorities.toString())
      uploadData.append('walletAddress', publicKey.toString())
      
      if (formData.image) {
        uploadData.append('image', formData.image)
      }

      // Call backend API
      const response = await fetch('http://localhost:3001/api/create-token', {
        method: 'POST',
        body: uploadData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create token')
      }

      const result = await response.json()

      setTransactionState({
        status: 'success',
        message: 'Token created successfully!',
        txSignature: result.signature,
        tokenAddress: result.tokenAddress,
        metadataUri: result.metadataUri
      })

    } catch (error) {
      console.error('Token creation error:', error)
      setTransactionState({
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to create token'
      })
    }
  }

  const resetTransaction = () => {
    setTransactionState({
      status: 'idle',
      message: ''
    })
  }

  if (!connected) {
    return (
      <div className="max-w-md mx-auto mt-16">
        <div className="card text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-green-400 rounded-full flex items-center justify-center mx-auto mb-4">
            <Wallet className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Connect Your Wallet</h2>
          <p className="text-gray-600 mb-6">
            Connect your Phantom wallet to start creating SPL tokens on Solana
          </p>
          <WalletMultiButton className="!bg-gradient-to-r !from-purple-600 !to-green-400 hover:!from-purple-700 hover:!to-green-500 !w-full" />
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Need a wallet?</p>
                <p>Download Phantom wallet from <a href="https://phantom.app" target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-900">phantom.app</a></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gradient mb-4">Create Your SPL Token</h1>
        <p className="text-xl text-gray-600">
          Deploy custom tokens on Solana blockchain with metadata and IPFS storage
        </p>
      </div>

      {transactionState.status === 'idle' ? (
        <TokenForm onSubmit={handleTokenCreation} />
      ) : (
        <TransactionStatus 
          state={transactionState} 
          onReset={resetTransaction}
        />
      )}
    </div>
  )
}

export default TokenCreator
