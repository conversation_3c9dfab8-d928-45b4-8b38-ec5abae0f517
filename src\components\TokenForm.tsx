import React, { useState } from 'react'
import { Upload, Image as ImageIcon, Info, DollarSign } from 'lucide-react'
import { TokenFormData } from './TokenCreator'

interface TokenFormProps {
  onSubmit: (data: TokenFormData) => void
}

const TokenForm: React.FC<TokenFormProps> = ({ onSubmit }) => {
  const [formData, setFormData] = useState<TokenFormData>({
    name: '',
    symbol: '',
    description: '',
    totalSupply: '1000000',
    decimals: 9,
    image: null,
    revokeAuthorities: true
  })

  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [errors, setErrors] = useState<Partial<TokenFormData>>({})

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              type === 'number' ? Number(value) : value
    }))

    // Clear error when user starts typing
    if (errors[name as keyof TokenFormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, image: 'Please select a valid image file' as any }))
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, image: 'Image size must be less than 5MB' as any }))
        return
      }

      setFormData(prev => ({ ...prev, image: file }))
      
      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)

      // Clear error
      setErrors(prev => ({ ...prev, image: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<TokenFormData> = {}

    if (!formData.name.trim()) newErrors.name = 'Token name is required' as any
    if (!formData.symbol.trim()) newErrors.symbol = 'Token symbol is required' as any
    if (formData.symbol.length > 10) newErrors.symbol = 'Symbol must be 10 characters or less' as any
    if (!formData.description.trim()) newErrors.description = 'Description is required' as any
    if (!formData.totalSupply || Number(formData.totalSupply) <= 0) {
      newErrors.totalSupply = 'Total supply must be greater than 0' as any
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSubmit(formData)
    }
  }

  const estimatedCost = 0.01 // Approximate SOL cost for token creation

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Basic Info */}
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Token Information</h3>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Token Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`input-field ${errors.name ? 'border-red-500' : ''}`}
                  placeholder="e.g., My Awesome Token"
                  maxLength={32}
                />
                {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name as string}</p>}
              </div>

              <div>
                <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-1">
                  Token Symbol *
                </label>
                <input
                  type="text"
                  id="symbol"
                  name="symbol"
                  value={formData.symbol}
                  onChange={handleInputChange}
                  className={`input-field ${errors.symbol ? 'border-red-500' : ''}`}
                  placeholder="e.g., MAT"
                  maxLength={10}
                  style={{ textTransform: 'uppercase' }}
                />
                {errors.symbol && <p className="text-red-500 text-sm mt-1">{errors.symbol as string}</p>}
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description *
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className={`input-field ${errors.description ? 'border-red-500' : ''}`}
                  placeholder="Describe your token's purpose and utility..."
                  maxLength={500}
                />
                {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description as string}</p>}
                <p className="text-sm text-gray-500 mt-1">{formData.description.length}/500 characters</p>
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Token Economics</h3>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="totalSupply" className="block text-sm font-medium text-gray-700 mb-1">
                  Total Supply *
                </label>
                <input
                  type="number"
                  id="totalSupply"
                  name="totalSupply"
                  value={formData.totalSupply}
                  onChange={handleInputChange}
                  className={`input-field ${errors.totalSupply ? 'border-red-500' : ''}`}
                  placeholder="1000000"
                  min="1"
                />
                {errors.totalSupply && <p className="text-red-500 text-sm mt-1">{errors.totalSupply as string}</p>}
              </div>

              <div>
                <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-1">
                  Decimal Places
                </label>
                <select
                  id="decimals"
                  name="decimals"
                  value={formData.decimals}
                  onChange={handleInputChange}
                  className="input-field"
                >
                  {[0, 2, 4, 6, 8, 9].map(num => (
                    <option key={num} value={num}>{num} decimals</option>
                  ))}
                </select>
                <p className="text-sm text-gray-500 mt-1">
                  9 decimals is standard for most tokens
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Image & Settings */}
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Token Logo</h3>
            
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors">
                {imagePreview ? (
                  <div className="space-y-4">
                    <img 
                      src={imagePreview} 
                      alt="Token logo preview" 
                      className="w-24 h-24 object-cover rounded-lg mx-auto"
                    />
                    <p className="text-sm text-gray-600">{formData.image?.name}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <ImageIcon className="w-12 h-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-gray-600">Upload token logo</p>
                      <p className="text-sm text-gray-500">PNG, JPG, GIF up to 5MB</p>
                    </div>
                  </div>
                )}
                
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="inline-flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg cursor-pointer transition-colors mt-4"
                >
                  <Upload className="w-4 h-4" />
                  <span>{imagePreview ? 'Change Image' : 'Choose Image'}</span>
                </label>
              </div>
              {errors.image && <p className="text-red-500 text-sm">{errors.image as string}</p>}
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Authority Settings</h3>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="revokeAuthorities"
                  name="revokeAuthorities"
                  checked={formData.revokeAuthorities}
                  onChange={handleInputChange}
                  className="mt-1"
                />
                <div>
                  <label htmlFor="revokeAuthorities" className="text-sm font-medium text-gray-700">
                    Revoke mint and freeze authorities
                  </label>
                  <p className="text-sm text-gray-500 mt-1">
                    Recommended for most tokens. This makes the token supply fixed and prevents freezing accounts.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card bg-gradient-to-r from-purple-50 to-green-50 border-purple-200">
            <div className="flex items-start space-x-3">
              <DollarSign className="w-5 h-5 text-purple-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900">Estimated Cost</h4>
                <p className="text-2xl font-bold text-purple-600">~{estimatedCost} SOL</p>
                <p className="text-sm text-gray-600 mt-1">
                  Includes token creation, metadata upload, and transaction fees
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <button
          type="submit"
          className="btn-primary px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
        >
          Create Token
        </button>
      </div>
    </form>
  )
}

export default TokenForm
