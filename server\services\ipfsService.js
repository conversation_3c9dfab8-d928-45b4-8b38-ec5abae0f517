import { PinataSDK } from 'pinata-web3'

// Initialize Pinata client
const getPinataClient = () => {
  const apiKey = process.env.VITE_PINATA_API_KEY
  const secretKey = process.env.VITE_PINATA_SECRET_KEY

  if (!apiKey || !secretKey) {
    console.warn('⚠️ Pinata credentials not found, using mock IPFS service')
    return null
  }

  return new PinataSDK({
    pinataApiKey: apiKey,
    pinataSecretApiKey: secretKey
  })
}

// Mock IPFS service for development/demo
const mockIPFSUpload = async (data, filename) => {
  console.log('🔧 Using mock IPFS service for:', filename)
  
  // Generate a mock IPFS hash
  const mockHash = 'Qm' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  const mockUri = `https://gateway.pinata.cloud/ipfs/${mockHash}`
  
  // Simulate upload delay
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  console.log('✅ Mock upload complete:', mockUri)
  return mockUri
}

export const uploadToIPFS = async (buffer, filename) => {
  try {
    const pinata = getPinataClient()
    
    if (!pinata) {
      return await mockIPFSUpload(buffer, filename)
    }

    console.log('📤 Uploading file to IPFS via Pinata:', filename)

    // Convert buffer to File object
    const file = new File([buffer], filename, { type: 'image/png' })
    
    const result = await pinata.upload.file(file, {
      metadata: {
        name: filename,
        keyvalues: {
          type: 'token-image',
          uploadedAt: new Date().toISOString()
        }
      }
    })

    const ipfsUri = `https://gateway.pinata.cloud/ipfs/${result.IpfsHash}`
    console.log('✅ File uploaded to IPFS:', ipfsUri)
    
    return ipfsUri

  } catch (error) {
    console.error('❌ IPFS upload failed:', error)
    
    // Fallback to mock service if Pinata fails
    console.log('🔄 Falling back to mock IPFS service')
    return await mockIPFSUpload(buffer, filename)
  }
}

export const uploadMetadataToIPFS = async (metadata, filename) => {
  try {
    const pinata = getPinataClient()
    
    if (!pinata) {
      return await mockIPFSUpload(JSON.stringify(metadata, null, 2), filename)
    }

    console.log('📤 Uploading metadata to IPFS via Pinata:', filename)

    const result = await pinata.upload.json(metadata, {
      metadata: {
        name: filename,
        keyvalues: {
          type: 'token-metadata',
          tokenName: metadata.name,
          tokenSymbol: metadata.symbol,
          uploadedAt: new Date().toISOString()
        }
      }
    })

    const ipfsUri = `https://gateway.pinata.cloud/ipfs/${result.IpfsHash}`
    console.log('✅ Metadata uploaded to IPFS:', ipfsUri)
    
    return ipfsUri

  } catch (error) {
    console.error('❌ Metadata IPFS upload failed:', error)
    
    // Fallback to mock service if Pinata fails
    console.log('🔄 Falling back to mock IPFS service')
    return await mockIPFSUpload(JSON.stringify(metadata, null, 2), filename)
  }
}

// Utility function to validate IPFS URI
export const validateIPFSUri = (uri) => {
  if (!uri) return false
  
  // Check for valid IPFS URI patterns
  const ipfsPatterns = [
    /^https:\/\/gateway\.pinata\.cloud\/ipfs\/Qm[a-zA-Z0-9]{44}$/,
    /^https:\/\/ipfs\.io\/ipfs\/Qm[a-zA-Z0-9]{44}$/,
    /^ipfs:\/\/Qm[a-zA-Z0-9]{44}$/
  ]
  
  return ipfsPatterns.some(pattern => pattern.test(uri))
}

// Utility function to get IPFS hash from URI
export const getIPFSHash = (uri) => {
  if (!uri) return null
  
  const match = uri.match(/Qm[a-zA-Z0-9]{44}/)
  return match ? match[0] : null
}
