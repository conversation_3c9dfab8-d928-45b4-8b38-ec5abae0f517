import React from 'react'
import { CheckCircle, XCircle, Loader, ExternalLink, RotateCcw, Copy } from 'lucide-react'
import { TransactionState } from './TokenCreator'
import toast from 'react-hot-toast'

interface TransactionStatusProps {
  state: TransactionState
  onReset: () => void
}

const TransactionStatus: React.FC<TransactionStatusProps> = ({ state, onReset }) => {
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success(`${label} copied to clipboard!`)
    }).catch(() => {
      toast.error('Failed to copy to clipboard')
    })
  }

  const getStatusIcon = () => {
    switch (state.status) {
      case 'uploading':
      case 'creating':
        return <Loader className="w-8 h-8 text-blue-600 animate-spin" />
      case 'success':
        return <CheckCircle className="w-8 h-8 text-green-600" />
      case 'error':
        return <XCircle className="w-8 h-8 text-red-600" />
      default:
        return null
    }
  }

  const getStatusColor = () => {
    switch (state.status) {
      case 'uploading':
      case 'creating':
        return 'border-blue-200 bg-blue-50'
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'error':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getProgressSteps = () => {
    const steps = [
      { id: 'upload', label: 'Upload Metadata', completed: false, active: false },
      { id: 'create', label: 'Create Token', completed: false, active: false },
      { id: 'complete', label: 'Complete', completed: false, active: false }
    ]

    switch (state.status) {
      case 'uploading':
        steps[0].active = true
        break
      case 'creating':
        steps[0].completed = true
        steps[1].active = true
        break
      case 'success':
        steps[0].completed = true
        steps[1].completed = true
        steps[2].completed = true
        break
      case 'error':
        // Mark current step as failed
        if (state.message.includes('upload') || state.message.includes('metadata')) {
          steps[0].active = true
        } else {
          steps[0].completed = true
          steps[1].active = true
        }
        break
    }

    return steps
  }

  const network = import.meta.env.VITE_SOLANA_NETWORK || 'devnet'
  const explorerUrl = network === 'mainnet-beta' ? 'https://explorer.solana.com' : `https://explorer.solana.com?cluster=${network}`

  return (
    <div className="max-w-2xl mx-auto">
      <div className={`card ${getStatusColor()}`}>
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            {getStatusIcon()}
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {state.status === 'uploading' && 'Uploading Metadata'}
            {state.status === 'creating' && 'Creating Token'}
            {state.status === 'success' && 'Token Created Successfully!'}
            {state.status === 'error' && 'Creation Failed'}
          </h2>
          <p className="text-gray-600">{state.message}</p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {getProgressSteps().map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                    step.completed 
                      ? 'bg-green-600 text-white' 
                      : step.active 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-200 text-gray-600'
                  }`}>
                    {step.completed ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <span className="text-xs text-gray-600 mt-2 text-center">{step.label}</span>
                </div>
                {index < getProgressSteps().length - 1 && (
                  <div className={`flex-1 h-0.5 mx-4 ${
                    step.completed ? 'bg-green-600' : 'bg-gray-200'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Success Details */}
        {state.status === 'success' && (
          <div className="space-y-4 mb-6">
            {state.txSignature && (
              <div className="bg-white rounded-lg p-4 border">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Transaction Signature</span>
                  <button
                    onClick={() => copyToClipboard(state.txSignature!, 'Transaction signature')}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded flex-1 truncate">
                    {state.txSignature}
                  </code>
                  <a
                    href={`${explorerUrl}/tx/${state.txSignature}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </a>
                </div>
              </div>
            )}

            {state.tokenAddress && (
              <div className="bg-white rounded-lg p-4 border">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Token Address</span>
                  <button
                    onClick={() => copyToClipboard(state.tokenAddress!, 'Token address')}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded flex-1 truncate">
                    {state.tokenAddress}
                  </code>
                  <a
                    href={`${explorerUrl}/address/${state.tokenAddress}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </a>
                </div>
              </div>
            )}

            {state.metadataUri && (
              <div className="bg-white rounded-lg p-4 border">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Metadata URI</span>
                  <button
                    onClick={() => copyToClipboard(state.metadataUri!, 'Metadata URI')}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded flex-1 truncate">
                    {state.metadataUri}
                  </code>
                  <a
                    href={state.metadataUri}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </a>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4">
          {(state.status === 'success' || state.status === 'error') && (
            <button
              onClick={onReset}
              className="btn-secondary flex items-center space-x-2"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Create Another Token</span>
            </button>
          )}
          
          {state.status === 'success' && state.txSignature && (
            <a
              href={`${explorerUrl}/tx/${state.txSignature}`}
              target="_blank"
              rel="noopener noreferrer"
              className="btn-primary flex items-center space-x-2"
            >
              <ExternalLink className="w-4 h-4" />
              <span>View on Explorer</span>
            </a>
          )}
        </div>
      </div>
    </div>
  )
}

export default TransactionStatus
