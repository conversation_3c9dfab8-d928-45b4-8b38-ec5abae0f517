import React from 'react'
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'
import { useConnection, useWallet } from '@solana/wallet-adapter-react'
import { LAMPORTS_PER_SOL } from '@solana/web3.js'
import { Coins, Wallet } from 'lucide-react'

const Header: React.FC = () => {
  const { connection } = useConnection()
  const { publicKey } = useWallet()
  const [balance, setBalance] = React.useState<number | null>(null)

  React.useEffect(() => {
    if (publicKey) {
      connection.getBalance(publicKey).then((balance) => {
        setBalance(balance / LAMPORTS_PER_SOL)
      }).catch(console.error)
    } else {
      setBalance(null)
    }
  }, [connection, publicKey])

  return (
    <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-green-400 rounded-xl flex items-center justify-center">
              <Coins className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gradient">Solana Token Creator</h1>
              <p className="text-sm text-gray-600">Create SPL tokens with ease</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {publicKey && balance !== null && (
              <div className="flex items-center space-x-2 bg-gray-100 rounded-lg px-3 py-2">
                <Wallet className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">
                  {balance.toFixed(4)} SOL
                </span>
              </div>
            )}
            <WalletMultiButton className="!bg-gradient-to-r !from-purple-600 !to-green-400 hover:!from-purple-700 hover:!to-green-500" />
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
