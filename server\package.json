{"name": "solana-token-creator-server", "version": "1.0.0", "type": "module", "description": "Backend server for Solana SPL token creation", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@metaplex-foundation/mpl-toolbox": "^0.10.0", "@metaplex-foundation/umi": "^1.2.0", "@metaplex-foundation/umi-bundle-defaults": "^1.2.0", "@metaplex-foundation/umi-uploader-irys": "^1.2.0", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "pinata-web3": "^0.4.0", "sharp": "^0.33.0"}, "devDependencies": {"nodemon": "^3.0.2"}}