import {
  createAndMint,
  mplTokenMetadata,
  TokenStandard
} from '@metaplex-foundation/mpl-token-metadata'
import {
  generateSigner,
  percentAmount,
  keypairIdentity,
  publicKey
} from '@metaplex-foundation/umi'
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults'
import { irysUploader } from '@metaplex-foundation/umi-uploader-irys'
import { base58 } from '@metaplex-foundation/umi/serializers'
import { 
  TOKEN_PROGRAM_ID, 
  AuthorityType, 
  createSetAuthorityInstruction 
} from '@solana/spl-token'
import { 
  Connection, 
  Keypair, 
  PublicKey, 
  Transaction, 
  sendAndConfirmTransaction 
} from '@solana/web3.js'

// Get RPC endpoint from environment
const getRpcEndpoint = () => {
  const network = process.env.VITE_SOLANA_NETWORK || 'devnet'
  const customRpc = process.env.VITE_SOLANA_RPC_URL
  
  if (customRpc) {
    return customRpc
  }
  
  switch (network) {
    case 'mainnet-beta':
      return 'https://api.mainnet-beta.solana.com'
    case 'testnet':
      return 'https://api.testnet.solana.com'
    case 'devnet':
    default:
      return 'https://api.devnet.solana.com'
  }
}

export const createToken = async ({
  name,
  symbol,
  uri,
  decimals = 9,
  totalSupply,
  revokeAuthorities = true,
  walletAddress
}) => {
  try {
    console.log('🔧 Initializing Solana connection...')
    
    const rpcEndpoint = getRpcEndpoint()
    console.log('🌐 Using RPC endpoint:', rpcEndpoint)

    // Initialize UMI
    const umi = createUmi(rpcEndpoint)
      .use(mplTokenMetadata())
      .use(irysUploader())

    // For server-side token creation, we need to use a server wallet
    // In production, this should be a secure server wallet with minimal funds
    // For demo purposes, we'll create a temporary keypair
    // NOTE: In a real application, you would either:
    // 1. Have the user sign the transaction on the frontend
    // 2. Use a secure server wallet with proper key management
    
    console.log('⚠️ Creating temporary keypair for demo purposes')
    console.log('⚠️ In production, implement proper wallet integration')
    
    // Create a temporary keypair (this is for demo only)
    const serverKeypair = Keypair.generate()
    const umiKeypair = umi.eddsa.createKeypairFromSecretKey(serverKeypair.secretKey)
    umi.use(keypairIdentity(umiKeypair))

    // Create web3.js connection for authority operations
    const connection = new Connection(rpcEndpoint, 'confirmed')

    console.log('🪙 Generating mint signer...')
    const mintSigner = generateSigner(umi)

    // Calculate total supply with decimals
    const totalSupplyWithDecimals = BigInt(totalSupply) * BigInt(10 ** decimals)

    console.log('📝 Token parameters:', {
      name,
      symbol,
      uri,
      decimals,
      totalSupply: totalSupplyWithDecimals.toString(),
      mintAddress: mintSigner.publicKey
    })

    // Create and mint token
    console.log('🚀 Creating and minting token...')
    const mintAndCreateIx = createAndMint(umi, {
      mint: mintSigner,
      name,
      symbol,
      uri,
      sellerFeeBasisPoints: percentAmount(0), // No royalties
      decimals,
      amount: totalSupplyWithDecimals,
      tokenOwner: publicKey(walletAddress), // Send tokens to user's wallet
      tokenStandard: TokenStandard.Fungible,
    })

    const tx = await mintAndCreateIx.sendAndConfirm(umi)
    const signature = base58.deserialize(tx.signature)[0]

    console.log('✅ Token created successfully!')
    console.log('📊 Transaction signature:', signature)
    console.log('🪙 Token address:', mintSigner.publicKey)

    let authorityRevocationSignature = null

    // Revoke authorities if requested
    if (revokeAuthorities) {
      console.log('🔒 Revoking mint and freeze authorities...')
      
      try {
        const mintPublicKey = new PublicKey(mintSigner.publicKey)
        const transaction = new Transaction()

        // Add instruction to revoke mint authority
        transaction.add(
          createSetAuthorityInstruction(
            mintPublicKey,
            serverKeypair.publicKey,
            AuthorityType.MintTokens,
            null
          )
        )

        // Add instruction to revoke freeze authority
        transaction.add(
          createSetAuthorityInstruction(
            mintPublicKey,
            serverKeypair.publicKey,
            AuthorityType.FreezeAccount,
            null
          )
        )

        // Send and confirm the authority revocation transaction
        authorityRevocationSignature = await sendAndConfirmTransaction(
          connection,
          transaction,
          [serverKeypair]
        )

        console.log('✅ Authorities revoked successfully!')
        console.log('📊 Authority revocation signature:', authorityRevocationSignature)
      } catch (authorityError) {
        console.warn('⚠️ Failed to revoke authorities:', authorityError.message)
        // Don't fail the entire operation if authority revocation fails
      }
    }

    return {
      signature,
      tokenAddress: mintSigner.publicKey,
      authorityRevocationSignature,
      success: true
    }

  } catch (error) {
    console.error('❌ Token creation failed:', error)
    throw new Error(`Token creation failed: ${error.message}`)
  }
}
